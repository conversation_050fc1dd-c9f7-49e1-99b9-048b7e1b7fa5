import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.DEV ? '' : 'http://49.235.144.212:8080', // 开发环境使用代理，生产环境直接请求
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()

    // 添加token到请求头
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }

    // 对于登录接口，使用form-urlencoded格式并添加必要的请求头
    if (config.url === '/api/admin/login' && config.method === 'post') {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      // 添加后端API要求的特定请求头
      config.headers['knife4j-gateway-code'] = 'ROOT'
      config.headers['request-origion'] = 'Knife4j'

      // 将JSON数据转换为form-urlencoded格式
      if (config.data && typeof config.data === 'object') {
        const params = new URLSearchParams()
        Object.keys(config.data).forEach(key => {
          if (config.data[key] !== undefined && config.data[key] !== null) {
            params.append(key, config.data[key])
          }
        })
        config.data = params.toString()
        console.log('转换后的请求体:', config.data)
        console.log('请求体长度:', config.data.length)
      }
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 调试信息
    console.log('发送请求:', {
      url: config.baseURL + config.url,
      method: config.method,
      data: config.data,
      params: config.params,
      headers: config.headers
    })

    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('收到响应:', {
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      headers: response.headers
    })

    const res = response.data

    // 如果返回的状态码不是200，说明接口有问题
    if (res.code && res.code !== 200) {
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })

      // 401: 未授权，token过期等
      if (res.code === 401) {
        ElMessageBox.confirm(
          '登录状态已过期，您可以继续留在该页面，或者重新登录',
          '系统提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          const userStore = useUserStore()
          userStore.logout()
          location.reload()
        })
      }

      return Promise.reject(new Error(res.message || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)

    let message = '请求失败'

    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请登录'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${error.response.status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message) {
      message = error.message
    }

    ElMessage({
      message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

export default service
