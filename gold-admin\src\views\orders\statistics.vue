<template>
  <div class="app-container">
    <div class="statistics-header">
      <h2>订单统计分析</h2>
      <p>回收订单数据统计与分析</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-orders">
            <el-icon class="card-panel-icon">
              <Document />
            </el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总订单数</div>
            <div class="card-panel-num">{{ statistics.totalOrders }}</div>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-completed">
            <el-icon class="card-panel-icon">
              <CircleCheck />
            </el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">已完成订单</div>
            <div class="card-panel-num">{{ statistics.completedOrders }}</div>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-amount">
            <el-icon class="card-panel-icon">
              <Money />
            </el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总交易金额</div>
            <div class="card-panel-num">¥{{ statistics.totalAmount }}</div>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-weight">
            <el-icon class="card-panel-icon">
              <Scale />
            </el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总回收重量(g)</div>
            <div class="card-panel-num">{{ statistics.totalWeight }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-container">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>订单状态分布</span>
            </div>
          </template>
          <div ref="statusChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>黄金类型分布</span>
            </div>
          </template>
          <div ref="typeChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>月度订单趋势</span>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>最近订单</span>
          <el-button type="primary" size="small" @click="$router.push('/orders')">
            查看全部
          </el-button>
        </div>
      </template>

      <el-table :data="recentOrders" border>
        <el-table-column prop="orderId" label="订单号" width="180" />
        <el-table-column prop="account" label="用户账号" width="120" />
        <el-table-column prop="goldType" label="黄金类型" width="100" />
        <el-table-column prop="estimatedWeight" label="重量(g)" width="100" align="right" />
        <el-table-column prop="finalPrice" label="成交价格" width="120" align="right">
          <template #default="scope">
            <span v-if="scope.row.finalPrice">¥{{ scope.row.finalPrice }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { Document, CircleCheck, Money, Scale } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { parseTime } from '@/utils'

const statusChartRef = ref()
const typeChartRef = ref()
const trendChartRef = ref()

const statistics = ref({
  totalOrders: 1256,
  completedOrders: 1089,
  totalAmount: 2456789.50,
  totalWeight: 15678.5
})

const recentOrders = ref([
  {
    id: 1,
    orderId: 'REC20231201001',
    account: 'user001',
    goldType: '黄金首饰',
    estimatedWeight: 10.5,
    finalPrice: 1500.00,
    status: 3,
    createTime: new Date().getTime()
  },
  {
    id: 2,
    orderId: 'REC20231201002',
    account: 'user002',
    goldType: '金条',
    estimatedWeight: 50.0,
    finalPrice: 7200.00,
    status: 3,
    createTime: new Date().getTime() - ********
  },
  {
    id: 3,
    orderId: 'REC20231201003',
    account: 'user003',
    goldType: '金币',
    estimatedWeight: 25.0,
    finalPrice: null,
    status: 2,
    createTime: new Date().getTime() - *********
  }
])

function initCharts() {
  nextTick(() => {
    initStatusChart()
    initTypeChart()
    initTrendChart()
  })
}

function initStatusChart() {
  const chart = echarts.init(statusChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1089, name: '已完成' },
          { value: 89, name: '处理中' },
          { value: 56, name: '待处理' },
          { value: 22, name: '已取消' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

function initTypeChart() {
  const chart = echarts.init(typeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '黄金类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 567, name: '黄金首饰' },
          { value: 345, name: '金条' },
          { value: 234, name: '金币' },
          { value: 110, name: '其他' }
        ]
      }
    ]
  }
  chart.setOption(option)
}

function initTrendChart() {
  const chart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['订单数量', '交易金额']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: [
      {
        type: 'value',
        name: '订单数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '交易金额(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '订单数量',
        type: 'bar',
        data: [89, 95, 123, 134, 156, 178, 189, 167, 145, 134, 123, 98]
      },
      {
        name: '交易金额',
        type: 'line',
        yAxisIndex: 1,
        data: [12.5, 14.2, 18.9, 21.3, 25.6, 28.9, 31.2, 27.8, 24.5, 22.1, 19.8, 16.5]
      }
    ]
  }
  chart.setOption(option)
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  }
  return statusMap[status] || '未知'
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.statistics-header {
  margin-bottom: 20px;
}

.statistics-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.statistics-header p {
  margin: 0;
  color: #909399;
}

.statistics-cards {
  margin-bottom: 20px;
}

.charts-container {
  margin-bottom: 20px;
}

.chart-card, .table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #999;
}

.icon-orders {
  background: #409EFF;
}

.icon-completed {
  background: #67C23A;
}

.icon-amount {
  background: #E6A23C;
}

.icon-weight {
  background: #F56C6C;
}
</style>
