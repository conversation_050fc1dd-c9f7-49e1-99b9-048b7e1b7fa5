<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 基本设置 -->
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>基本设置</span>
            </div>
          </template>
          
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            label-width="120px"
          >
            <el-form-item label="系统名称">
              <el-input v-model="basicForm.systemName" />
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input
                v-model="basicForm.systemDesc"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item label="客服电话">
              <el-input v-model="basicForm.servicePhone" />
            </el-form-item>
            <el-form-item label="客服邮箱">
              <el-input v-model="basicForm.serviceEmail" />
            </el-form-item>
            <el-form-item label="系统Logo">
              <el-upload
                class="avatar-uploader"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleLogoSuccess"
                :before-upload="beforeImageUpload"
              >
                <img v-if="basicForm.logo" :src="basicForm.logo" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 黄金价格设置 -->
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>黄金价格设置</span>
            </div>
          </template>
          
          <el-form
            ref="priceFormRef"
            :model="priceForm"
            label-width="120px"
          >
            <el-form-item label="足金价格(元/g)">
              <el-input-number
                v-model="priceForm.goldPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="K金价格(元/g)">
              <el-input-number
                v-model="priceForm.kGoldPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="铂金价格(元/g)">
              <el-input-number
                v-model="priceForm.platinumPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="银价格(元/g)">
              <el-input-number
                v-model="priceForm.silverPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="价格更新时间">
              <span>{{ formatTime(priceForm.updateTime) }}</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="savePriceSettings">
                保存价格
              </el-button>
              <el-button type="success" @click="syncGoldPrice">
                同步最新价格
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 邮件设置 -->
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>邮件设置</span>
            </div>
          </template>
          
          <el-form
            ref="emailFormRef"
            :model="emailForm"
            label-width="120px"
          >
            <el-form-item label="SMTP服务器">
              <el-input v-model="emailForm.smtpHost" />
            </el-form-item>
            <el-form-item label="SMTP端口">
              <el-input-number
                v-model="emailForm.smtpPort"
                :min="1"
                :max="65535"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="发件人邮箱">
              <el-input v-model="emailForm.fromEmail" />
            </el-form-item>
            <el-form-item label="邮箱密码">
              <el-input
                v-model="emailForm.emailPassword"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item label="启用SSL">
              <el-switch v-model="emailForm.enableSSL" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveEmailSettings">
                保存设置
              </el-button>
              <el-button type="success" @click="testEmail">
                测试邮件
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 系统统计 -->
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>系统统计</span>
            </div>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="系统版本">
              v1.0.0
            </el-descriptions-item>
            <el-descriptions-item label="运行时间">
              {{ systemStats.uptime }}
            </el-descriptions-item>
            <el-descriptions-item label="总用户数">
              {{ systemStats.totalUsers }}
            </el-descriptions-item>
            <el-descriptions-item label="总订单数">
              {{ systemStats.totalOrders }}
            </el-descriptions-item>
            <el-descriptions-item label="今日订单">
              {{ systemStats.todayOrders }}
            </el-descriptions-item>
            <el-descriptions-item label="数据库大小">
              {{ systemStats.dbSize }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div style="margin-top: 20px;">
            <el-button type="warning" @click="clearCache">
              清理缓存
            </el-button>
            <el-button type="danger" @click="exportData">
              导出数据
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { parseTime } from '@/utils'

const basicFormRef = ref()
const priceFormRef = ref()
const emailFormRef = ref()

const basicForm = ref({
  systemName: '黄金回收销售管理后台',
  systemDesc: '专业的黄金回收销售管理系统',
  servicePhone: '************',
  serviceEmail: '<EMAIL>',
  logo: ''
})

const priceForm = ref({
  goldPrice: 450.00,
  kGoldPrice: 320.00,
  platinumPrice: 200.00,
  silverPrice: 5.50,
  updateTime: new Date().getTime()
})

const emailForm = ref({
  smtpHost: 'smtp.qq.com',
  smtpPort: 587,
  fromEmail: '',
  emailPassword: '',
  enableSSL: true
})

const systemStats = ref({
  uptime: '7天12小时',
  totalUsers: 1234,
  totalOrders: 567,
  todayOrders: 23,
  dbSize: '125.6 MB'
})

async function saveBasicSettings() {
  try {
    // 这里应该调用实际的API
    // await updateSystemSettings(basicForm.value)
    ElMessage.success('基本设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

async function savePriceSettings() {
  try {
    // 这里应该调用实际的API
    // await updatePriceSettings(priceForm.value)
    priceForm.value.updateTime = new Date().getTime()
    ElMessage.success('价格设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

async function syncGoldPrice() {
  try {
    ElMessage.info('正在同步最新价格...')
    // 这里应该调用黄金价格API
    // const response = await fetchLatestGoldPrice()
    
    // 模拟同步
    setTimeout(() => {
      priceForm.value.goldPrice = 455.50
      priceForm.value.updateTime = new Date().getTime()
      ElMessage.success('价格同步成功')
    }, 2000)
  } catch (error) {
    ElMessage.error('价格同步失败')
  }
}

async function saveEmailSettings() {
  try {
    // 这里应该调用实际的API
    // await updateEmailSettings(emailForm.value)
    ElMessage.success('邮件设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

async function testEmail() {
  try {
    ElMessage.info('正在发送测试邮件...')
    // 这里应该调用测试邮件API
    // await sendTestEmail()
    
    setTimeout(() => {
      ElMessage.success('测试邮件发送成功')
    }, 2000)
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  }
}

async function clearCache() {
  try {
    await ElMessageBox.confirm('确定要清理系统缓存吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用清理缓存API
    // await clearSystemCache()
    ElMessage.success('缓存清理成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('缓存清理失败')
    }
  }
}

async function exportData() {
  try {
    await ElMessageBox.confirm('确定要导出系统数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.info('正在导出数据，请稍候...')
    // 这里应该调用导出数据API
    // const response = await exportSystemData()
    
    setTimeout(() => {
      ElMessage.success('数据导出成功')
    }, 3000)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('数据导出失败')
    }
  }
}

function handleLogoSuccess(res) {
  basicForm.value.logo = res.data.url
}

function beforeImageUpload(file) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

async function loadSettings() {
  try {
    // 这里应该调用实际的API加载设置
    // const response = await getSystemSettings()
    // basicForm.value = response.data.basic
    // priceForm.value = response.data.price
    // emailForm.value = response.data.email
    // systemStats.value = response.data.stats
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
}
</style>
