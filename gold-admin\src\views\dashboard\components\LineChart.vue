<template>
  <div ref="chartRef" class="chart" />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
})

const chartRef = ref()
let chart = null

function initChart() {
  chart = echarts.init(chartRef.value)
  setOptions()
}

function setOptions() {
  const option = {
    xAxis: {
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      boundaryGap: false,
      axisTick: {
        show: false
      }
    },
    grid: {
      left: 10,
      right: 10,
      bottom: 20,
      top: 30,
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10]
    },
    yAxis: {
      axisTick: {
        show: false
      }
    },
    legend: {
      data: ['预期', '实际']
    },
    series: [
      {
        name: '预期',
        itemStyle: {
          color: '#FF005A',
          lineStyle: {
            color: '#FF005A',
            width: 2
          }
        },
        smooth: true,
        type: 'line',
        data: props.chartData.newVisitis?.expectedData || [],
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: '实际',
        smooth: true,
        type: 'line',
        itemStyle: {
          color: '#3888fa',
          lineStyle: {
            color: '#3888fa',
            width: 2
          },
          areaStyle: {
            color: '#f3f8ff'
          }
        },
        data: props.chartData.newVisitis?.actualData || [],
        animationDuration: 2800,
        animationEasing: 'quadraticOut'
      }
    ]
  }
  
  chart.setOption(option)
}

function resize() {
  if (chart) {
    chart.resize()
  }
}

watch(() => props.chartData, () => {
  if (chart) {
    setOptions()
  }
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', resize)
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', resize)
})
</script>

<style scoped>
.chart {
  height: 100%;
  width: 100%;
}
</style>
