import request from '@/utils/request'

// 获取产品列表
export function getProductList(params) {
  return request({
    url: '/admin/products',
    method: 'get',
    params
  })
}

// 获取产品详情
export function getProductDetail(id) {
  return request({
    url: `/admin/products/${id}`,
    method: 'get'
  })
}

// 创建产品
export function createProduct(data) {
  return request({
    url: '/admin/products',
    method: 'post',
    data
  })
}

// 更新产品
export function updateProduct(id, data) {
  return request({
    url: `/admin/products/${id}`,
    method: 'put',
    data
  })
}

// 删除产品
export function deleteProduct(id) {
  return request({
    url: `/admin/products/${id}`,
    method: 'delete'
  })
}

// 上架/下架产品
export function toggleProductStatus(id, status) {
  return request({
    url: `/admin/products/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 获取产品分类
export function getProductCategories() {
  return request({
    url: '/admin/product-categories',
    method: 'get'
  })
}
