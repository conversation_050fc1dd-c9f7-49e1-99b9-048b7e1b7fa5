<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
          <div class="card-panel-icon-wrapper icon-people">
            <el-icon class="card-panel-icon"><User /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">用户总数</div>
            <div class="card-panel-num">{{ dashboardData.totalUsers }}</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('messages')">
          <div class="card-panel-icon-wrapper icon-message">
            <el-icon class="card-panel-icon"><Document /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">订单总数</div>
            <div class="card-panel-num">{{ dashboardData.totalOrders }}</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('purchases')">
          <div class="card-panel-icon-wrapper icon-money">
            <el-icon class="card-panel-icon"><Money /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">今日交易额</div>
            <div class="card-panel-num">¥{{ dashboardData.todayAmount }}</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('shoppings')">
          <div class="card-panel-icon-wrapper icon-shopping">
            <el-icon class="card-panel-icon"><Goods /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">产品总数</div>
            <div class="card-panel-num">{{ dashboardData.totalProducts }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
            </div>
          </template>
          <div class="chart-container">
            <line-chart :chart-data="lineChartData" />
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>订单状态分布</span>
            </div>
          </template>
          <div class="chart-container">
            <pie-chart :chart-data="pieChartData" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新订单 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>最新订单</span>
          <el-button type="primary" size="small" @click="$router.push('/orders')">
            查看更多
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentOrders" style="width: 100%">
        <el-table-column prop="orderId" label="订单号" width="180" />
        <el-table-column prop="account" label="用户账号" width="120" />
        <el-table-column prop="goldType" label="黄金类型" width="100" />
        <el-table-column prop="estimatedPrice" label="预估价格" width="120">
          <template #default="scope">
            ¥{{ scope.row.estimatedPrice }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { parseTime } from '@/utils'
import LineChart from './components/LineChart.vue'
import PieChart from './components/PieChart.vue'

const dashboardData = ref({
  totalUsers: 0,
  totalOrders: 0,
  todayAmount: 0,
  totalProducts: 0
})

const lineChartData = ref({
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
})

const pieChartData = ref([
  { value: 335, name: '待处理' },
  { value: 310, name: '处理中' },
  { value: 234, name: '已完成' },
  { value: 135, name: '已取消' }
])

const recentOrders = ref([])

function handleSetLineChartData(type) {
  // 这里可以根据类型切换图表数据
  console.log('切换图表数据:', type)
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  }
  return statusMap[status] || '未知'
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

async function fetchDashboardData() {
  try {
    // 这里应该调用实际的API
    // const response = await getDashboardStats()
    
    // 模拟数据
    dashboardData.value = {
      totalUsers: 1234,
      totalOrders: 567,
      todayAmount: 12345.67,
      totalProducts: 89
    }
    
    recentOrders.value = [
      {
        orderId: 'ORD20231201001',
        account: 'user001',
        goldType: '黄金首饰',
        estimatedPrice: 1500.00,
        status: 1,
        createTime: new Date().getTime() - 3600000
      },
      {
        orderId: 'ORD20231201002',
        account: 'user002',
        goldType: '金条',
        estimatedPrice: 2800.00,
        status: 2,
        createTime: new Date().getTime() - 7200000
      }
    ]
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.panel-group {
  margin-bottom: 20px;
}

.card-panel-col {
  margin-bottom: 32px;
}

.chart-container {
  height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
