<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入产品名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.category"
        placeholder="产品分类"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="黄金首饰" value="jewelry" />
        <el-option label="金条" value="bar" />
        <el-option label="金币" value="coin" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="上架" :value="1" />
        <el-option label="下架" :value="0" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        添加产品
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="产品图片" width="100" align="center">
        <template #default="scope">
          <el-image
            :src="scope.row.image || '/placeholder.png'"
            style="width: 60px; height: 60px;"
            fit="cover"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="产品名称" width="200">
        <template #default="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      
      <el-table-column label="分类" width="100">
        <template #default="scope">
          {{ getCategoryText(scope.row.category) }}
        </template>
      </el-table-column>
      
      <el-table-column label="价格" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.price }}
        </template>
      </el-table-column>
      
      <el-table-column label="库存" width="80" align="center">
        <template #default="scope">
          {{ scope.row.stock }}
        </template>
      </el-table-column>
      
      <el-table-column label="销量" width="80" align="center">
        <template #default="scope">
          {{ scope.row.sales }}
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleUpdate(scope.row)">
            编辑
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="danger"
            @click="handleModifyStatus(scope.row, 0)"
          >
            下架
          </el-button>
          <el-button
            v-else
            size="small"
            type="success"
            @click="handleModifyStatus(scope.row, 1)"
          >
            上架
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 产品编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '创建产品' : '编辑产品'"
      v-model="dialogFormVisible"
      width="600px"
    >
      <el-form
        ref="dataFormRef"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="80px"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="temp.name" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="temp.category" placeholder="请选择分类">
            <el-option label="黄金首饰" value="jewelry" />
            <el-option label="金条" value="bar" />
            <el-option label="金币" value="coin" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number
            v-model="temp.price"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number
            v-model="temp.stock"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="产品图片">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="temp.image" :src="temp.image" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择">
            <el-option label="上架" :value="1" />
            <el-option label="下架" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProductList, createProduct, updateProduct, toggleProductStatus } from '@/api/products'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: '',
  category: '',
  status: ''
})

const dialogFormVisible = ref(false)
const dialogStatus = ref('')
const temp = ref({
  id: undefined,
  name: '',
  category: '',
  price: 0,
  stock: 0,
  description: '',
  image: '',
  status: 1
})

const dataFormRef = ref()

const rules = ref({
  name: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  price: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
  stock: [{ required: true, message: '库存不能为空', trigger: 'blur' }]
})

async function getList() {
  listLoading.value = true
  try {
    const response = await getProductList(listQuery.value)
    list.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取产品列表失败:', error)
    // 模拟数据
    list.value = [
      {
        id: 1,
        name: '黄金戒指',
        category: 'jewelry',
        price: 1500.00,
        stock: 10,
        sales: 5,
        description: '经典款黄金戒指',
        image: 'https://via.placeholder.com/60x60',
        status: 1,
        createTime: new Date().getTime()
      }
    ]
    total.value = 1
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

function handleCreate() {
  resetTemp()
  dialogStatus.value = 'create'
  dialogFormVisible.value = true
}

function handleUpdate(row) {
  temp.value = Object.assign({}, row)
  dialogStatus.value = 'update'
  dialogFormVisible.value = true
}

function resetTemp() {
  temp.value = {
    id: undefined,
    name: '',
    category: '',
    price: 0,
    stock: 0,
    description: '',
    image: '',
    status: 1
  }
}

async function createData() {
  dataFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await createProduct(temp.value)
        ElMessage.success('创建成功')
        dialogFormVisible.value = false
        getList()
      } catch (error) {
        ElMessage.error('创建失败')
      }
    }
  })
}

async function updateData() {
  dataFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateProduct(temp.value.id, temp.value)
        ElMessage.success('更新成功')
        dialogFormVisible.value = false
        getList()
      } catch (error) {
        ElMessage.error('更新失败')
      }
    }
  })
}

async function handleModifyStatus(row, status) {
  try {
    await ElMessageBox.confirm(
      `确定要${status === 1 ? '上架' : '下架'}该产品吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await toggleProductStatus(row.id, status)
    ElMessage.success('操作成功')
    row.status = status
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

function handleImageSuccess(res) {
  temp.value.image = res.data.url
}

function beforeImageUpload(file) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

function getCategoryText(category) {
  const categoryMap = {
    jewelry: '黄金首饰',
    bar: '金条',
    coin: '金币',
    other: '其他'
  }
  return categoryMap[category] || category
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
