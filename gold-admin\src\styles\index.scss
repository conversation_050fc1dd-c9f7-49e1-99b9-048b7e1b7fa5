// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
}

// 主题色彩
:root {
  --primary-color: #D4380D;
  --primary-light: #FF4D4F;
  --primary-dark: #AD2102;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;
  
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-disabled: #bfbfbf;
  
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  
  --bg-color: #ffffff;
  --bg-light: #fafafa;
  --bg-dark: #f5f5f5;
}

// 布局相关
.layout-container {
  height: 100vh;
  overflow: hidden;
}

.main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 210px;
  position: relative;
}

.sidebar-container {
  transition: width 0.28s;
  width: 210px !important;
  background-color: #001529;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

.hideSidebar {
  .sidebar-container {
    width: 54px !important;
  }
  
  .main-container {
    margin-left: 54px;
  }
}

// 面包屑
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
  
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}

// 页面容器
.app-container {
  padding: 20px;
}

// 工具栏
.filter-container {
  padding-bottom: 10px;
  
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

// 表格
.table-container {
  .el-table {
    .el-table__header-wrapper {
      th {
        background-color: #fafafa;
      }
    }
  }
}

// 分页
.pagination-container {
  padding: 32px 16px;
  text-align: right;
}

// 卡片
.card-panel {
  height: 108px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
  border-color: rgba(0, 0, 0, .05);
  
  &:hover {
    .card-panel-icon-wrapper {
      color: #fff;
    }
    
    .icon-people {
      background: #40c9c6;
    }
    
    .icon-message {
      background: #36a3f7;
    }
    
    .icon-money {
      background: #f4516c;
    }
    
    .icon-shopping {
      background: #34bfa3;
    }
  }
  
  .icon-people {
    color: #40c9c6;
  }
  
  .icon-message {
    color: #36a3f7;
  }
  
  .icon-money {
    color: #f4516c;
  }
  
  .icon-shopping {
    color: #34bfa3;
  }
  
  .card-panel-icon-wrapper {
    float: left;
    margin: 14px 0 0 14px;
    padding: 16px;
    transition: all 0.38s ease-out;
    border-radius: 6px;
  }
  
  .card-panel-icon {
    float: left;
    font-size: 48px;
  }
  
  .card-panel-description {
    float: right;
    font-weight: bold;
    margin: 26px;
    margin-left: 0px;
    
    .card-panel-text {
      line-height: 18px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
      margin-bottom: 12px;
    }
    
    .card-panel-num {
      font-size: 20px;
    }
  }
}

// 响应式
@media (max-width: 1024px) {
  .sidebar-container {
    transition: transform 0.28s;
    width: 210px !important;
  }
  
  .hideSidebar .sidebar-container {
    pointer-events: none;
    transition-duration: 0.3s;
    transform: translate3d(-210px, 0, 0);
  }
}
