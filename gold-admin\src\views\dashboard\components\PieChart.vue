<template>
  <div ref="chartRef" class="chart" />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  }
})

const chartRef = ref()
let chart = null

function initChart() {
  chart = echarts.init(chartRef.value)
  setOptions()
}

function setOptions() {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      left: 'center',
      bottom: '10',
      data: props.chartData.map(item => item.name)
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        roseType: 'radius',
        radius: [15, 95],
        center: ['50%', '38%'],
        data: props.chartData,
        animationEasing: 'cubicInOut',
        animationDuration: 2600
      }
    ]
  }
  
  chart.setOption(option)
}

function resize() {
  if (chart) {
    chart.resize()
  }
}

watch(() => props.chartData, () => {
  if (chart) {
    setOptions()
  }
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', resize)
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', resize)
})
</script>

<style scoped>
.chart {
  height: 100%;
  width: 100%;
}
</style>
