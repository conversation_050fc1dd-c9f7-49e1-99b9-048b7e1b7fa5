<template>
  <div class="sidebar-wrapper">
    <!-- Logo -->
    <div class="sidebar-logo">
      <router-link to="/" class="sidebar-logo-link">
        <img src="/logo.png" alt="logo" class="sidebar-logo-img" />
        <h1 v-show="!isCollapse" class="sidebar-title">黄金管理后台</h1>
      </router-link>
    </div>

    <!-- 菜单 -->
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
        background-color="#001529"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <template v-for="route in routes" :key="route.path">
          <template v-for="child in route.children" :key="child.path">
            <el-menu-item :index="route.path + (child.path ? '/' + child.path : '')">
              <el-icon v-if="child.meta?.icon">
                <component :is="child.meta.icon" />
              </el-icon>
              <span>{{ child.meta?.title }}</span>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const isCollapse = computed(() => !appStore.sidebar.opened)

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

const routes = computed(() => {
  return router.options.routes.filter(route => {
    // 过滤掉登录页面和其他隐藏的路由
    if (route.path === '/login' || route.meta?.hidden) {
      return false
    }
    // 只显示有children的路由（Layout路由）
    return route.children && route.children.length > 0
  })
})
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  background-color: #001529;
}

.sidebar-logo {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: #002140;
  text-align: center;
  overflow: hidden;

  .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;

    .sidebar-logo-img {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 8px;
    }

    .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      font-size: 14px;
      vertical-align: middle;
    }
  }
}

.el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  color: #fff !important;
  &:hover {
    background-color: #263445 !important;
  }
}

:deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
  color: #fff !important;
}

:deep(.el-sub-menu__title) {
  color: #fff !important;
}
</style>
