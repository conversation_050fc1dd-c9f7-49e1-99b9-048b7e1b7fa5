# 黄金回收销售管理后台系统

## 项目简介

这是一个基于 Vue 3 + Vite + Element Plus 构建的黄金回收销售管理后台系统，为黄金回收销售裂变小程序提供完整的后台管理功能。

## 技术栈

- **前端框架**: Vue 3.4+
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **图表库**: ECharts 5.4+
- **代码规范**: ESLint + Prettier
- **样式预处理**: Sass

## 功能特性

### 🎯 核心功能
- **用户管理**: 用户列表、用户详情、用户状态管理
- **订单管理**: 黄金回收订单处理、状态跟踪、订单详情
- **产品管理**: 产品上架下架、库存管理、分类管理
- **数据统计**: 实时数据展示、图表分析、业务报表

### 🔧 系统功能
- **权限管理**: 基于角色的访问控制
- **系统设置**: 基本配置、价格设置、邮件配置
- **数据导出**: 支持Excel格式数据导出
- **响应式设计**: 适配桌面端和移动端

### 🎨 界面特性
- **现代化UI**: 基于Element Plus的现代化界面
- **主题定制**: 支持黄金主题色彩配置
- **布局灵活**: 可折叠侧边栏、面包屑导航
- **交互友好**: 丰富的交互反馈和动画效果

## 项目结构

```
gold-admin/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── auth.js        # 认证相关
│   │   ├── users.js       # 用户管理
│   │   ├── orders.js      # 订单管理
│   │   └── products.js    # 产品管理
│   ├── components/        # 通用组件
│   │   ├── Hamburger/     # 汉堡菜单
│   │   ├── Pagination/    # 分页组件
│   │   └── Screenfull/    # 全屏组件
│   ├── layout/            # 布局组件
│   │   ├── components/    # 布局子组件
│   │   └── index.vue      # 主布局
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   │   ├── app.js         # 应用状态
│   │   └── user.js        # 用户状态
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── dashboard/     # 仪表盘
│   │   ├── login/         # 登录页
│   │   ├── users/         # 用户管理
│   │   ├── orders/        # 订单管理
│   │   ├── products/      # 产品管理
│   │   └── settings/      # 系统设置
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .eslintrc.cjs          # ESLint配置
├── .prettierrc            # Prettier配置
├── package.json           # 项目依赖
├── vite.config.js         # Vite配置
└── README.md              # 项目说明
```

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 进入项目目录
cd gold-admin

# 安装依赖
npm install
# 或
yarn install
```

### 开发环境
```bash
# 启动开发服务器
npm run dev
# 或
yarn dev
```

访问 http://localhost:3000

### 生产构建
```bash
# 构建生产版本
npm run build
# 或
yarn build
```

### 代码检查
```bash
# ESLint检查
npm run lint
# 或
yarn lint

# 代码格式化
npm run format
# 或
yarn format
```

## API配置

### 后端接口
项目直接请求后端API服务器，无需代理：
- API基础地址: `http://**************:8080`
- 所有环境都直接请求此地址

### 环境变量配置
- `.env.development`: 开发环境配置
- `.env.production`: 生产环境配置
- `VITE_API_BASE_URL`: 后端API基础地址

### 接口说明
- **认证接口**: `/admin/login`、`/admin/info`
- **用户管理**: `/admin/users`
- **订单管理**: `/admin/orders`
- **产品管理**: `/admin/products`

## 部署说明

### 开发部署
1. 构建项目: `npm run build`
2. 将 `dist` 目录部署到Web服务器
3. 配置反向代理将 `/api` 请求转发到后端服务

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://**************:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发指南

### 添加新页面
1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在侧边栏菜单中添加导航项

### 添加新API
1. 在 `src/api/` 下创建对应的API文件
2. 使用统一的请求封装 `src/utils/request.js`
3. 在组件中导入并使用

### 状态管理
使用 Pinia 进行状态管理，在 `src/stores/` 下创建对应的store文件。

### 样式规范
- 使用 Sass 预处理器
- 遵循 BEM 命名规范
- 统一使用项目主题色彩变量

## 常见问题

### 1. 开发服务器启动失败
- 检查 Node.js 版本是否符合要求
- 删除 `node_modules` 重新安装依赖
- 检查端口是否被占用

### 2. API请求失败
- 检查后端服务是否正常运行
- 确认代理配置是否正确
- 查看浏览器控制台错误信息

### 3. 构建失败
- 检查代码是否有语法错误
- 运行 `npm run lint` 检查代码规范
- 确认所有依赖都已正确安装

## 更新日志

### v1.0.0 (2023-12-01)
- 🎉 项目初始化
- ✨ 完成基础框架搭建
- ✨ 实现用户管理功能
- ✨ 实现订单管理功能
- ✨ 实现产品管理功能
- ✨ 实现系统设置功能
- ✨ 完成响应式布局

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 项目地址: [GitHub Repository]

---

**黄金回收销售管理后台系统** - 让黄金回收管理更简单高效！
