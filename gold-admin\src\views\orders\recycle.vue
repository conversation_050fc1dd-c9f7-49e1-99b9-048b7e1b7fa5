<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入订单号或用户账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="订单状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="待处理" :value="1" />
        <el-option label="处理中" :value="2" />
        <el-option label="已完成" :value="3" />
        <el-option label="已取消" :value="4" />
      </el-select>
      <el-select
        v-model="listQuery.goldType"
        placeholder="黄金类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="黄金首饰" value="黄金首饰" />
        <el-option label="金条" value="金条" />
        <el-option label="金币" value="金币" />
        <el-option label="其他" value="其他" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="Download"
        @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="订单号" width="180">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">
            {{ scope.row.orderId }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="用户账号" width="120">
        <template #default="scope">
          {{ scope.row.account }}
        </template>
      </el-table-column>

      <el-table-column label="黄金类型" width="100">
        <template #default="scope">
          {{ scope.row.goldType }}
        </template>
      </el-table-column>

      <el-table-column label="成色" width="80">
        <template #default="scope">
          {{ scope.row.purity }}
        </template>
      </el-table-column>

      <el-table-column label="预估重量(g)" width="120" align="right">
        <template #default="scope">
          {{ scope.row.estimatedWeight }}
        </template>
      </el-table-column>

      <el-table-column label="预估价格" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.estimatedPrice }}
        </template>
      </el-table-column>

      <el-table-column label="最终价格" width="120" align="right">
        <template #default="scope">
          <span v-if="scope.row.finalPrice">¥{{ scope.row.finalPrice }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="success"
            @click="handleProcess(scope.row)"
          >
            处理
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            size="small"
            type="warning"
            @click="handleComplete(scope.row)"
          >
            完成
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 处理订单对话框 -->
    <el-dialog
      title="处理回收订单"
      v-model="processDialogVisible"
      width="500px"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        label-width="100px"
      >
        <el-form-item label="实际重量(g)">
          <el-input-number
            v-model="processForm.actualWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际成色">
          <el-input v-model="processForm.actualPurity" />
        </el-form-item>
        <el-form-item label="最终价格">
          <el-input-number
            v-model="processForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="检测结果">
          <el-input
            v-model="processForm.inspectionResult"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmProcess">
            确认处理
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderList, updateOrderStatus, exportOrders } from '@/api/orders'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const router = useRouter()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  goldType: ''
})

const processDialogVisible = ref(false)
const processForm = ref({
  orderId: '',
  actualWeight: 0,
  actualPurity: '',
  finalPrice: 0,
  inspectionResult: ''
})
const currentOrder = ref(null)

async function getList() {
  listLoading.value = true
  try {
    const response = await getOrderList(listQuery.value)
    list.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取回收订单列表失败:', error)
    // 模拟数据
    list.value = [
      {
        id: 1,
        orderId: 'REC20231201001',
        account: 'user001',
        goldType: '黄金首饰',
        goldCondition: '完好',
        purity: '足金',
        estimatedWeight: 10.5,
        estimatedPrice: 1500.00,
        finalPrice: null,
        status: 1,
        description: '黄金戒指',
        inspectionResult: '',
        receiverName: '张三',
        receiverPhone: '***********',
        receiverAddress: '北京市朝阳区xxx',
        createTime: new Date().getTime(),
        updateTime: new Date().getTime()
      },
      {
        id: 2,
        orderId: 'REC20231201002',
        account: 'user002',
        goldType: '金条',
        goldCondition: '完好',
        purity: '999',
        estimatedWeight: 50.0,
        estimatedPrice: 7200.00,
        finalPrice: 7150.00,
        status: 3,
        description: '投资金条',
        inspectionResult: '成色符合标准',
        receiverName: '李四',
        receiverPhone: '***********',
        receiverAddress: '上海市浦东新区xxx',
        createTime: new Date().getTime() - ********,
        updateTime: new Date().getTime()
      }
    ]
    total.value = 2
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

function handleDetail(row) {
  router.push(`/order-detail/${row.orderId}`)
}

function handleProcess(row) {
  currentOrder.value = row
  processForm.value = {
    orderId: row.orderId,
    actualWeight: row.estimatedWeight,
    actualPurity: row.purity,
    finalPrice: row.estimatedPrice,
    inspectionResult: ''
  }
  processDialogVisible.value = true
}

async function confirmProcess() {
  try {
    await updateOrderStatus(currentOrder.value.id, {
      status: 2,
      ...processForm.value
    })
    ElMessage.success('回收订单处理成功')
    processDialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('处理失败')
  }
}

async function handleComplete(row) {
  try {
    await ElMessageBox.confirm('确定要完成该回收订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(row.id, { status: 3 })
    ElMessage.success('回收订单已完成')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

async function handleDownload() {
  try {
    const response = await exportOrders(listQuery.value)
    // 处理文件下载
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `回收订单数据_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  }
  return statusMap[status] || '未知'
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.text-muted {
  color: #999;
}
</style>
