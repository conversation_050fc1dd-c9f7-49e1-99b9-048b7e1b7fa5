<template>
  <div class="app-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>订单详情</span>
          <el-button type="primary" @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div v-if="orderDetail" class="order-detail">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions title="订单信息" :column="1" border>
              <el-descriptions-item label="订单号">
                {{ orderDetail.orderId }}
              </el-descriptions-item>
              <el-descriptions-item label="用户账号">
                {{ orderDetail.account }}
              </el-descriptions-item>
              <el-descriptions-item label="订单状态">
                <el-tag :type="getStatusType(orderDetail.status)">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatTime(orderDetail.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(orderDetail.updateTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="收货信息" :column="1" border>
              <el-descriptions-item label="收货人">
                {{ orderDetail.receiverName }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ orderDetail.receiverPhone }}
              </el-descriptions-item>
              <el-descriptions-item label="收货地址">
                {{ orderDetail.receiverAddress }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 黄金信息 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-descriptions title="黄金信息" :column="1" border>
              <el-descriptions-item label="黄金类型">
                {{ orderDetail.goldType }}
              </el-descriptions-item>
              <el-descriptions-item label="黄金状况">
                {{ orderDetail.goldCondition }}
              </el-descriptions-item>
              <el-descriptions-item label="成色">
                {{ orderDetail.purity }}
              </el-descriptions-item>
              <el-descriptions-item label="描述">
                {{ orderDetail.description }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="价格信息" :column="1" border>
              <el-descriptions-item label="预估重量">
                {{ orderDetail.estimatedWeight }}g
              </el-descriptions-item>
              <el-descriptions-item label="预估价格">
                ¥{{ orderDetail.estimatedPrice }}
              </el-descriptions-item>
              <el-descriptions-item label="最终价格">
                <span v-if="orderDetail.finalPrice">¥{{ orderDetail.finalPrice }}</span>
                <span v-else class="text-muted">待定</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 检测结果 -->
        <div v-if="orderDetail.inspectionResult" style="margin-top: 20px;">
          <el-descriptions title="检测结果" :column="1" border>
            <el-descriptions-item label="检测结果">
              {{ orderDetail.inspectionResult }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 图片展示 -->
        <div v-if="orderDetail.imageBase64" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <el-image
            :src="orderDetail.imageBase64"
            style="width: 200px; height: 200px; margin: 10px;"
            :preview-src-list="[orderDetail.imageBase64]"
            fit="cover"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons" style="margin-top: 30px;">
          <el-button
            v-if="orderDetail.status === 1"
            type="success"
            @click="handleProcess"
          >
            开始处理
          </el-button>
          <el-button
            v-if="orderDetail.status === 2"
            type="primary"
            @click="handleComplete"
          >
            完成订单
          </el-button>
          <el-button
            v-if="orderDetail.status < 3"
            type="danger"
            @click="handleCancel"
          >
            取消订单
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 处理订单对话框 -->
    <el-dialog
      title="处理订单"
      v-model="processDialogVisible"
      width="500px"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        label-width="100px"
      >
        <el-form-item label="实际重量(g)">
          <el-input-number
            v-model="processForm.actualWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际成色">
          <el-input v-model="processForm.actualPurity" />
        </el-form-item>
        <el-form-item label="最终价格">
          <el-input-number
            v-model="processForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="检测结果">
          <el-input
            v-model="processForm.inspectionResult"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmProcess">
            确认处理
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderDetail, updateOrderStatus } from '@/api/orders'
import { parseTime } from '@/utils'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const orderDetail = ref(null)
const processDialogVisible = ref(false)
const processForm = ref({
  actualWeight: 0,
  actualPurity: '',
  finalPrice: 0,
  inspectionResult: ''
})

async function fetchOrderDetail() {
  loading.value = true
  try {
    const response = await getOrderDetail(route.params.id)
    orderDetail.value = response.data
  } catch (error) {
    console.error('获取订单详情失败:', error)
    // 模拟数据
    orderDetail.value = {
      id: 1,
      orderId: route.params.id,
      account: 'user001',
      goldType: '黄金首饰',
      goldCondition: '完好',
      purity: '足金',
      estimatedWeight: 10.5,
      estimatedPrice: 1500.00,
      finalPrice: null,
      status: 1,
      description: '黄金戒指，款式经典',
      inspectionResult: '',
      receiverName: '张三',
      receiverPhone: '***********',
      receiverAddress: '北京市朝阳区xxx街道xxx号',
      imageBase64: 'https://via.placeholder.com/200x200',
      createTime: new Date().getTime(),
      updateTime: new Date().getTime()
    }
  } finally {
    loading.value = false
  }
}

function handleProcess() {
  processForm.value = {
    actualWeight: orderDetail.value.estimatedWeight,
    actualPurity: orderDetail.value.purity,
    finalPrice: orderDetail.value.estimatedPrice,
    inspectionResult: ''
  }
  processDialogVisible.value = true
}

async function confirmProcess() {
  try {
    await updateOrderStatus(orderDetail.value.id, {
      status: 2,
      ...processForm.value
    })
    ElMessage.success('订单处理成功')
    processDialogVisible.value = false
    fetchOrderDetail()
  } catch (error) {
    ElMessage.error('处理失败')
  }
}

async function handleComplete() {
  try {
    await ElMessageBox.confirm('确定要完成该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateOrderStatus(orderDetail.value.id, { status: 3 })
    ElMessage.success('订单已完成')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

async function handleCancel() {
  try {
    await ElMessageBox.confirm('确定要取消该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateOrderStatus(orderDetail.value.id, { status: 4 })
    ElMessage.success('订单已取消')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  }
  return statusMap[status] || '未知'
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #999;
}

.action-buttons {
  text-align: center;
}
</style>
