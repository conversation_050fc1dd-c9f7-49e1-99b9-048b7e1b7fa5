import request from '@/utils/request'

// 管理员登录
export function login(data) {
  return request({
    url: '/api/admin/login',
    method: 'post',
    data
  })
}

// 获取管理员信息
export function getUserInfo() {
  return request({
    url: '/admin/info',
    method: 'get'
  })
}

// 管理员登出
export function logout() {
  return request({
    url: '/admin/logout',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/admin/change-password',
    method: 'post',
    data
  })
}
