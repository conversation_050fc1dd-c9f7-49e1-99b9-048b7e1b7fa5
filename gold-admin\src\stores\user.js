import { defineStore } from 'pinia'
import { login, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('admin_token') || '',
    userInfo: null,
    permissions: []
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    userName: (state) => state.userInfo?.name || '',
    userRole: (state) => state.userInfo?.role || ''
  },

  actions: {
    // 登录
    async login(loginForm) {
      try {
        const response = await login(loginForm)
        const { token, adminId } = response.data

        this.token = token
        // 设置基本的用户信息，包含adminId
        this.userInfo = {
          id: adminId,
          adminId: adminId
        }

        localStorage.setItem('admin_token', token)

        return Promise.resolve(response)
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getUserInfo()
        this.userInfo = response.data
        return Promise.resolve(response)
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // 登出
    logout() {
      this.token = ''
      this.userInfo = null
      this.permissions = []
      localStorage.removeItem('admin_token')
    },

    // 重置状态
    resetState() {
      this.token = ''
      this.userInfo = null
      this.permissions = []
    }
  }
})
