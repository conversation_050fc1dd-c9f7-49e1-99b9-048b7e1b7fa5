{"name": "gold-admin", "version": "1.0.0", "description": "黄金回收销售管理后台系统", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "axios": "^1.6.2", "@element-plus/icons-vue": "^2.3.1", "dayjs": "^1.11.10", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "prettier": "^3.1.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "sass": "^1.69.5"}, "keywords": ["vue3", "vite", "element-plus", "admin", "gold-recycling"], "author": "Gold Admin Team", "license": "MIT"}